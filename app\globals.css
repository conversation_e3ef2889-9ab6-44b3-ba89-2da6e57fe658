@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 10 10 10;
    --foreground: 255 255 255;
    --card: 45 45 45;
    --card-foreground: 255 255 255;
    --popover: 45 45 45;
    --popover-foreground: 255 255 255;
    --primary: 212 175 55;
    --primary-foreground: 10 10 10;
    --secondary: 45 45 45;
    --secondary-foreground: 255 255 255;
    --muted: 45 45 45;
    --muted-foreground: 204 204 204;
    --accent: 212 175 55;
    --accent-foreground: 10 10 10;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 45 45 45;
    --input: 45 45 45;
    --ring: 212 175 55;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
  }
}

/* Animações personalizadas para Magic UI */
@keyframes rippling {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.animate-rippling {
  animation: rippling 0.6s linear;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient var(--speed, 8s) ease infinite;
}

/* Importar Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
