"use client";

import { useState } from "react";
import Image from "next/image";
import { Shi<PERSON>Button } from "@/components/ui/shimmer-button";
import { RippleButton } from "@/components/ui/ripple-button";
import { PulsatingButton } from "@/components/ui/pulsating-button";
import { SparklesText } from "@/components/ui/sparkles-text";
import { TypingAnimation } from "@/components/ui/typing-animation";
import { WordRotate } from "@/components/ui/word-rotate";
import { AnimatedGradientText } from "@/components/ui/animated-gradient-text";
import { RetroGrid } from "@/components/ui/retro-grid";
import { Ripple } from "@/components/ui/ripple";
import { BorderBeam } from "@/components/ui/border-beam";
import { Meteors } from "@/components/ui/meteors";
import { MagicCard } from "@/components/ui/magic-card";
import { NeonGradientCard } from "@/components/ui/neon-gradient-card";
import Particles from "@/components/ui/particles";

// Configurações dos links - EDITE AQUI PARA PERSONALIZAR
const LINKS_CONFIG = {
  whatsapp: {
    phone: '5511999999999',
    message: 'Olá! Gostaria de agendar um horário no Estúdio730.'
  },
  instagram: {
    username: 'estudio730'
  },
  location: {
    address: 'Rua das Flores, 123, São Paulo, SP'
  },
  website: {
    url: 'https://www.estudio730.com.br'
  }
};

// Função para gerar link do WhatsApp
function generateWhatsAppLink(phone: string, message: string) {
  const encodedMessage = encodeURIComponent(message);
  return `https://wa.me/${phone}?text=${encodedMessage}`;
}

// Função para gerar link do Instagram
function generateInstagramLink(username: string) {
  return `https://www.instagram.com/${username}/`;
}

// Função para gerar link do Google Maps
function generateLocationLink(address: string) {
  const encodedAddress = encodeURIComponent(address);
  return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
}

export default function Home() {
  const [configOpen, setConfigOpen] = useState(false);

  const handleLinkClick = (type: string) => {
    let url = '';
    
    switch (type) {
      case 'whatsapp':
        url = generateWhatsAppLink(LINKS_CONFIG.whatsapp.phone, LINKS_CONFIG.whatsapp.message);
        break;
      case 'instagram':
        url = generateInstagramLink(LINKS_CONFIG.instagram.username);
        break;
      case 'location':
        url = generateLocationLink(LINKS_CONFIG.location.address);
        break;
      case 'website':
        url = LINKS_CONFIG.website.url;
        break;
    }
    
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
      {/* Background Effects */}
      <RetroGrid
        className="opacity-20"
        angle={65}
        cellSize={60}
        lightLineColor="#d4af37"
        darkLineColor="#d4af37"
      />
      <Ripple
        mainCircleSize={300}
        mainCircleOpacity={0.1}
        numCircles={6}
        className="opacity-30"
      />
      <Particles
        className="absolute inset-0 hidden sm:block"
        quantity={50}
        ease={80}
        color="#d4af37"
        size={0.8}
      />
      <div className="sm:hidden">
        <Particles
          className="absolute inset-0"
          quantity={25}
          ease={60}
          color="#d4af37"
          size={0.6}
        />
      </div>
      <div className="hidden sm:block">
        <Meteors number={15} />
      </div>
      <div className="sm:hidden">
        <Meteors number={8} />
      </div>

      {/* Botão de Configurações */}
      <div className="fixed top-4 right-4 z-50">
        <MagicCard
          className="w-12 h-12 p-0 border-gray-700/30"
          gradientColor="#d4af37"
          gradientOpacity={0.2}
        >
          <button
            className="w-full h-full bg-gray-800/90 backdrop-blur-sm border-none rounded-xl text-white hover:bg-gray-700/90 transition-all duration-300 flex items-center justify-center group"
            onClick={() => setConfigOpen(!configOpen)}
            title="Configurações"
          >
            <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </MagicCard>
      </div>

      {/* Container principal */}
      <div className="relative z-10 max-w-md mx-auto px-4 sm:px-5 py-8 sm:py-10 min-h-screen flex flex-col justify-center gap-6 sm:gap-8">
        
        {/* Header com logo e nome da barbearia */}
        <header className="text-center">
          <div className="mx-auto mb-4 sm:mb-6 w-fit">
            <NeonGradientCard
              className="w-32 h-32 sm:w-40 sm:h-40 hover:scale-105 transition-transform duration-300"
              borderSize={3}
              borderRadius={80}
              neonColors={{
                firstColor: "#d4af37",
                secondColor: "#ffd700"
              }}
            >
              <div className="w-full h-full rounded-full overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-transparent rounded-full"></div>
                <Image
                  src="/logo.webp"
                  alt="Estúdio730 Logo"
                  width={160}
                  height={160}
                  className="w-full h-full object-cover rounded-full"
                  priority
                />
              </div>
            </NeonGradientCard>
          </div>
          
          <SparklesText
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-2 sm:mb-3"
            colors={{ first: "#d4af37", second: "#ffd700" }}
            sparklesCount={15}
          >
            Estúdio730
          </SparklesText>

          <WordRotate
            words={[
              "Estilo e tradição em cada corte",
              "Excelência em barbearia moderna",
              "Seu visual, nossa especialidade",
              "Cortes que fazem a diferença"
            ]}
            className="text-gray-300 text-base sm:text-lg px-2"
            duration={3000}
          />
        </header>

        {/* Seção de links principais */}
        <main className="space-y-3 sm:space-y-4">
          
          {/* Botão WhatsApp */}
          <MagicCard
            className="w-full h-14 sm:h-16 p-0 border-green-500/30"
            gradientColor="#25d366"
            gradientOpacity={0.3}
          >
            <ShimmerButton
              onClick={() => handleLinkClick('whatsapp')}
              className="w-full h-full bg-green-600 hover:bg-green-700 border-none text-white font-semibold text-base sm:text-lg"
              shimmerColor="#25d366"
              background="linear-gradient(135deg, #25d366, #128c7e)"
            >
            <div className="flex items-center justify-between w-full px-2">
              <div className="flex items-center gap-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                <div className="text-left">
                  <div className="font-bold">WhatsApp</div>
                  <AnimatedGradientText
                    className="text-xs px-2 py-0.5"
                    colors={["#25d366", "#128c7e", "#25d366"]}
                    animationSpeed={3}
                  >
                    Agende seu horário
                  </AnimatedGradientText>
                </div>
              </div>
              <svg className="w-5 h-5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
            </ShimmerButton>
            <BorderBeam
              size={300}
              duration={12}
              colorFrom="#25d366"
              colorTo="#128c7e"
            />
          </MagicCard>

          {/* Botão Instagram */}
          <MagicCard
            className="w-full h-14 sm:h-16 p-0 border-purple-500/30"
            gradientColor="#e4405f"
            gradientOpacity={0.3}
          >
            <RippleButton
              onClick={() => handleLinkClick('instagram')}
              className="w-full h-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 border-none text-white font-semibold text-base sm:text-lg"
              rippleColor="#e4405f"
            >
            <div className="flex items-center justify-between w-full px-2">
              <div className="flex items-center gap-4">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
                <div className="text-left">
                  <div className="font-bold">Instagram</div>
                  <div className="text-sm opacity-80">Veja nossos trabalhos</div>
                </div>
              </div>
              <svg className="w-5 h-5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
            </RippleButton>
            <BorderBeam
              size={300}
              duration={15}
              colorFrom="#e4405f"
              colorTo="#9c40ff"
            />
          </MagicCard>

          {/* Botão Localização */}
          <MagicCard
            className="w-full h-14 sm:h-16 p-0 border-blue-500/30"
            gradientColor="#4285f4"
            gradientOpacity={0.3}
          >
            <PulsatingButton
              onClick={() => handleLinkClick('location')}
              className="w-full h-full bg-blue-600 hover:bg-blue-700 border-none text-white font-semibold text-base sm:text-lg"
              pulseColor="#4285f4"
            >
            <div className="flex items-center justify-between w-full px-2">
              <div className="flex items-center gap-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <div className="text-left">
                  <div className="font-bold">Localização</div>
                  <div className="text-sm opacity-80">Como chegar</div>
                </div>
              </div>
              <svg className="w-5 h-5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
            </PulsatingButton>
            <BorderBeam
              size={300}
              duration={18}
              colorFrom="#4285f4"
              colorTo="#1976d2"
            />
          </MagicCard>

          {/* Botão Site */}
          <MagicCard
            className="w-full h-14 sm:h-16 p-0 border-yellow-500/30"
            gradientColor="#d4af37"
            gradientOpacity={0.3}
          >
            <RippleButton
              onClick={() => handleLinkClick('website')}
              className="w-full h-full bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 border-none text-black font-semibold text-base sm:text-lg"
              rippleColor="#d4af37"
            >
            <div className="flex items-center justify-between w-full px-2">
              <div className="flex items-center gap-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
                <div className="text-left">
                  <div className="font-bold">Site Oficial</div>
                  <div className="text-sm opacity-80">Conheça mais</div>
                </div>
              </div>
              <svg className="w-5 h-5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
            </RippleButton>
            <BorderBeam
              size={300}
              duration={20}
              colorFrom="#d4af37"
              colorTo="#ffd700"
            />
          </MagicCard>

        </main>

        {/* Footer */}
        <footer className="text-center text-gray-400 text-xs sm:text-sm">
          <MagicCard
            className="p-3 sm:p-4 border-gray-700/30 bg-gray-900/50 backdrop-blur-sm"
            gradientColor="#d4af37"
            gradientOpacity={0.2}
          >
            <TypingAnimation
              className="text-sm"
              duration={80}
              delay={2000}
              startOnView={true}
            >
              © 2024 Estúdio730. Todos os direitos reservados.
            </TypingAnimation>
            <div className="flex justify-center gap-4 sm:gap-6 mt-3 sm:mt-4 opacity-60">
            <svg className="w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-yellow-400 transition-all duration-300 cursor-pointer" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
            <svg className="w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-blue-400 transition-all duration-300 cursor-pointer" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
            <svg className="w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-green-400 transition-all duration-300 cursor-pointer" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            </div>
          </MagicCard>
        </footer>
      </div>
    </div>
  );
}
