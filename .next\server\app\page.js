/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1Q2xpbmslNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXN0dWRpbzczMC1saW5rLW1vZGVybml6YWRvLz81NzUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1Q2xpbmslNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1Q2xpbmslNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamV0b3MlNUMlNUNsaW5rJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2pldG9zJTVDJTVDbGluayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2pldG9zJTVDJTVDbGluayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamV0b3MlNUMlNUNsaW5rJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9IO0FBQ3BIO0FBQ0Esb09BQXFIO0FBQ3JIO0FBQ0EsME9BQXdIO0FBQ3hIO0FBQ0Esd09BQXVIO0FBQ3ZIO0FBQ0Esa1BBQTRIO0FBQzVIO0FBQ0Esc1FBQXNJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXN0dWRpbzczMC1saW5rLW1vZGVybml6YWRvLz9hY2ExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2pldG9zXFxcXGxpbmtcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRvc1xcXFxsaW5rXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/shimmer-button */ \"(ssr)/./components/ui/shimmer-button.tsx\");\n/* harmony import */ var _components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ripple-button */ \"(ssr)/./components/ui/ripple-button.tsx\");\n/* harmony import */ var _components_ui_pulsating_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pulsating-button */ \"(ssr)/./components/ui/pulsating-button.tsx\");\n/* harmony import */ var _components_ui_sparkles_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sparkles-text */ \"(ssr)/./components/ui/sparkles-text.tsx\");\n/* harmony import */ var _components_ui_typing_animation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/typing-animation */ \"(ssr)/./components/ui/typing-animation.tsx\");\n/* harmony import */ var _components_ui_word_rotate__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/word-rotate */ \"(ssr)/./components/ui/word-rotate.tsx\");\n/* harmony import */ var _components_ui_animated_gradient_text__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/animated-gradient-text */ \"(ssr)/./components/ui/animated-gradient-text.tsx\");\n/* harmony import */ var _components_ui_retro_grid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/retro-grid */ \"(ssr)/./components/ui/retro-grid.tsx\");\n/* harmony import */ var _components_ui_ripple__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ripple */ \"(ssr)/./components/ui/ripple.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n// Configurações dos links - EDITE AQUI PARA PERSONALIZAR\nconst LINKS_CONFIG = {\n    whatsapp: {\n        phone: \"5511999999999\",\n        message: \"Ol\\xe1! Gostaria de agendar um hor\\xe1rio no Est\\xfadio730.\"\n    },\n    instagram: {\n        username: \"estudio730\"\n    },\n    location: {\n        address: \"Rua das Flores, 123, S\\xe3o Paulo, SP\"\n    },\n    website: {\n        url: \"https://www.estudio730.com.br\"\n    }\n};\n// Função para gerar link do WhatsApp\nfunction generateWhatsAppLink(phone, message) {\n    const encodedMessage = encodeURIComponent(message);\n    return `https://wa.me/${phone}?text=${encodedMessage}`;\n}\n// Função para gerar link do Instagram\nfunction generateInstagramLink(username) {\n    return `https://www.instagram.com/${username}/`;\n}\n// Função para gerar link do Google Maps\nfunction generateLocationLink(address) {\n    const encodedAddress = encodeURIComponent(address);\n    return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;\n}\nfunction Home() {\n    const [configOpen, setConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLinkClick = (type)=>{\n        let url = \"\";\n        switch(type){\n            case \"whatsapp\":\n                url = generateWhatsAppLink(LINKS_CONFIG.whatsapp.phone, LINKS_CONFIG.whatsapp.message);\n                break;\n            case \"instagram\":\n                url = generateInstagramLink(LINKS_CONFIG.instagram.username);\n                break;\n            case \"location\":\n                url = generateLocationLink(LINKS_CONFIG.location.address);\n                break;\n            case \"website\":\n                url = LINKS_CONFIG.website.url;\n                break;\n        }\n        if (url) {\n            window.open(url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_retro_grid__WEBPACK_IMPORTED_MODULE_10__.RetroGrid, {\n                className: \"opacity-20\",\n                angle: 65,\n                cellSize: 60,\n                lightLineColor: \"#d4af37\",\n                darkLineColor: \"#d4af37\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple__WEBPACK_IMPORTED_MODULE_11__.Ripple, {\n                mainCircleSize: 300,\n                mainCircleOpacity: 0.1,\n                numCircles: 6,\n                className: \"opacity-30\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"fixed top-5 right-5 z-50 w-12 h-12 bg-gray-800/80 backdrop-blur-sm border border-gray-700 rounded-full text-white hover:bg-gray-700/80 transition-all duration-300 flex items-center justify-center\",\n                onClick: ()=>setConfigOpen(!configOpen),\n                title: \"Configura\\xe7\\xf5es\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-md mx-auto px-5 py-10 min-h-screen flex flex-col justify-center gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden shadow-2xl border-4 border-yellow-500/30 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-transparent rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/logo.webp\",\n                                        alt: \"Est\\xfadio730 Logo\",\n                                        width: 160,\n                                        height: 160,\n                                        className: \"w-full h-full object-cover\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sparkles_text__WEBPACK_IMPORTED_MODULE_6__.SparklesText, {\n                                className: \"text-5xl font-bold mb-3\",\n                                colors: {\n                                    first: \"#d4af37\",\n                                    second: \"#ffd700\"\n                                },\n                                sparklesCount: 15,\n                                children: \"Est\\xfadio730\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_word_rotate__WEBPACK_IMPORTED_MODULE_8__.WordRotate, {\n                                words: [\n                                    \"Estilo e tradi\\xe7\\xe3o em cada corte\",\n                                    \"Excel\\xeancia em barbearia moderna\",\n                                    \"Seu visual, nossa especialidade\",\n                                    \"Cortes que fazem a diferen\\xe7a\"\n                                ],\n                                className: \"text-gray-300 text-lg\",\n                                duration: 3000\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_3__.ShimmerButton, {\n                                onClick: ()=>handleLinkClick(\"whatsapp\"),\n                                className: \"w-full h-16 bg-green-600 hover:bg-green-700 border-green-500/30 text-white font-semibold text-lg\",\n                                shimmerColor: \"#25d366\",\n                                background: \"linear-gradient(135deg, #25d366, #128c7e)\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold\",\n                                                            children: \"WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_gradient_text__WEBPACK_IMPORTED_MODULE_9__.AnimatedGradientText, {\n                                                            className: \"text-xs px-2 py-0.5\",\n                                                            colors: [\n                                                                \"#25d366\",\n                                                                \"#128c7e\",\n                                                                \"#25d366\"\n                                                            ],\n                                                            animationSpeed: 3,\n                                                            children: \"Agende seu hor\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 opacity-60\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__.RippleButton, {\n                                onClick: ()=>handleLinkClick(\"instagram\"),\n                                className: \"w-full h-16 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 border-purple-500/30 text-white font-semibold text-lg\",\n                                rippleColor: \"#e4405f\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Instagram\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm opacity-80\",\n                                                            children: \"Veja nossos trabalhos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 opacity-60\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pulsating_button__WEBPACK_IMPORTED_MODULE_5__.PulsatingButton, {\n                                onClick: ()=>handleLinkClick(\"location\"),\n                                className: \"w-full h-16 bg-blue-600 hover:bg-blue-700 border-blue-500/30 text-white font-semibold text-lg\",\n                                pulseColor: \"#4285f4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Localiza\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm opacity-80\",\n                                                            children: \"Como chegar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 opacity-60\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__.RippleButton, {\n                                onClick: ()=>handleLinkClick(\"website\"),\n                                className: \"w-full h-16 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 border-yellow-500/30 text-black font-semibold text-lg\",\n                                rippleColor: \"#d4af37\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold\",\n                                                            children: \"Site Oficial\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm opacity-80\",\n                                                            children: \"Conhe\\xe7a mais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 opacity-60\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"text-center text-gray-400 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typing_animation__WEBPACK_IMPORTED_MODULE_7__.TypingAnimation, {\n                                className: \"text-sm\",\n                                duration: 80,\n                                delay: 2000,\n                                startOnView: true,\n                                children: \"\\xa9 2024 Est\\xfadio730. Todos os direitos reservados.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mt-3 opacity-60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/animated-gradient-text.tsx":
/*!**************************************************!*\
  !*** ./components/ui/animated-gradient-text.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedGradientText: () => (/* binding */ AnimatedGradientText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction AnimatedGradientText({ children, className, colors = [\n    \"#ffaa40\",\n    \"#9c40ff\",\n    \"#ffaa40\"\n], animationSpeed = 8, showBorder = false, ...props }) {\n    const gradientStyle = {\n        \"--color-one\": colors[0],\n        \"--color-two\": colors[1] || colors[0],\n        \"--color-three\": colors[2] || colors[0],\n        \"--speed\": `${animationSpeed}s`\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"group relative mx-auto flex max-w-fit flex-row items-center justify-center rounded-2xl bg-white/40 px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#8fdfff1f] backdrop-blur-sm transition-shadow duration-500 ease-out [--bg-size:300%] hover:shadow-[inset_0_-5px_10px_#8fdfff3f] dark:bg-black/40\", className),\n        style: gradientStyle,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 block h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] p-[1px] ![mask-composite:subtract] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)]`\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            showBorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 z-10 h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] ![mask-composite:subtract]`\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/animated-gradient-text.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/pulsating-button.tsx":
/*!********************************************!*\
  !*** ./components/ui/pulsating-button.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PulsatingButton: () => (/* binding */ PulsatingButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst PulsatingButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, pulseColor = \"#808080\", duration = \"1.5s\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-pointer items-center justify-center rounded-lg bg-primary px-4 py-2 text-center text-primary-foreground\", className),\n        style: {\n            \"--pulse-color\": pulseColor,\n            \"--duration\": duration\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-1/2 top-1/2 size-full -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-lg bg-inherit\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n        lineNumber: 25,\n        columnNumber: 7\n    }, undefined);\n});\nPulsatingButton.displayName = \"PulsatingButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/pulsating-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/retro-grid.tsx":
/*!**************************************!*\
  !*** ./components/ui/retro-grid.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RetroGrid: () => (/* binding */ RetroGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction RetroGrid({ className, angle = 65, cellSize = 60, opacity = 0.5, lightLineColor = \"gray\", darkLineColor = \"gray\", ...props }) {\n    const gridStyles = {\n        \"--grid-angle\": `${angle}deg`,\n        \"--cell-size\": `${cellSize}px`,\n        \"--opacity\": opacity,\n        \"--light-line\": lightLineColor,\n        \"--dark-line\": darkLineColor\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none absolute size-full overflow-hidden [perspective:200px]\", `opacity-[var(--opacity)]`, className),\n        style: gridStyles,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 [transform:rotateX(var(--grid-angle))]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/retro-grid.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/ripple-button.tsx":
/*!*****************************************!*\
  !*** ./components/ui/ripple-button.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RippleButton: () => (/* binding */ RippleButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ RippleButton auto */ \n\n\nconst RippleButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef(({ className, children, rippleColor = \"#ffffff\", duration = \"600ms\", onClick, ...props }, ref)=>{\n    const [buttonRipples, setButtonRipples] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const handleClick = (event)=>{\n        createRipple(event);\n        onClick?.(event);\n    };\n    const createRipple = (event)=>{\n        const button = event.currentTarget;\n        const rect = button.getBoundingClientRect();\n        const size = Math.max(rect.width, rect.height);\n        const x = event.clientX - rect.left - size / 2;\n        const y = event.clientY - rect.top - size / 2;\n        const newRipple = {\n            x,\n            y,\n            size,\n            key: Date.now()\n        };\n        setButtonRipples((prevRipples)=>[\n                ...prevRipples,\n                newRipple\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (buttonRipples.length > 0) {\n            const lastRipple = buttonRipples[buttonRipples.length - 1];\n            const timeout = setTimeout(()=>{\n                setButtonRipples((prevRipples)=>prevRipples.filter((ripple)=>ripple.key !== lastRipple.key));\n            }, parseInt(duration));\n            return ()=>clearTimeout(timeout);\n        }\n    }, [\n        buttonRipples,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative flex cursor-pointer items-center justify-center overflow-hidden rounded-lg border-2 bg-background px-4 py-2 text-center text-primary\", className),\n        onClick: handleClick,\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute inset-0\",\n                children: buttonRipples.map((ripple)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute animate-rippling rounded-full bg-background opacity-30\",\n                        style: {\n                            width: `${ripple.size}px`,\n                            height: `${ripple.size}px`,\n                            top: `${ripple.y}px`,\n                            left: `${ripple.x}px`,\n                            backgroundColor: rippleColor,\n                            transform: `scale(0)`\n                        }\n                    }, ripple.key, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n        lineNumber: 60,\n        columnNumber: 7\n    }, undefined);\n});\nRippleButton.displayName = \"RippleButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/ripple-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/ripple.tsx":
/*!**********************************!*\
  !*** ./components/ui/ripple.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ripple: () => (/* binding */ Ripple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Ripple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function Ripple({ mainCircleSize = 210, mainCircleOpacity = 0.24, numCircles = 8, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]\", className),\n        ...props,\n        children: Array.from({\n            length: numCircles\n        }, (_, i)=>{\n            const size = mainCircleSize + i * 70;\n            const opacity = mainCircleOpacity - i * 0.03;\n            const animationDelay = `${i * 0.06}s`;\n            const borderStyle = \"solid\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute animate-ripple rounded-full border bg-foreground/25 shadow-xl`,\n                style: {\n                    \"--i\": i,\n                    width: `${size}px`,\n                    height: `${size}px`,\n                    opacity,\n                    animationDelay,\n                    borderStyle,\n                    borderWidth: \"1px\",\n                    borderColor: `var(--foreground)`,\n                    top: \"50%\",\n                    left: \"50%\",\n                    transform: \"translate(-50%, -50%) scale(1)\"\n                }\n            }, i, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple.tsx\",\n                lineNumber: 33,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n});\nRipple.displayName = \"Ripple\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/ripple.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/shimmer-button.tsx":
/*!******************************************!*\
  !*** ./components/ui/shimmer-button.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShimmerButton: () => (/* binding */ ShimmerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst ShimmerButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ shimmerColor = \"#ffffff\", shimmerSize = \"0.05em\", shimmerDuration = \"3s\", borderRadius = \"100px\", background = \"rgba(0, 0, 0, 1)\", className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 px-6 py-3 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\", \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\", className),\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-z-30 blur-[2px]\", \"absolute inset-0 overflow-visible [container-type:size]\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"insert-0 absolute size-full\", \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\", // transition\n                \"transform-gpu transition-all duration-300 ease-in-out\", // on hover\n                \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\", // on click\n                \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n        lineNumber: 33,\n        columnNumber: 7\n    }, undefined);\n});\nShimmerButton.displayName = \"ShimmerButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/shimmer-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sparkles-text.tsx":
/*!*****************************************!*\
  !*** ./components/ui/sparkles-text.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SparklesText: () => (/* binding */ SparklesText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SparklesText auto */ \n\n\n\nconst Sparkle = ({ id, x, y, color, delay, scale })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.svg, {\n        className: \"pointer-events-none absolute z-20\",\n        initial: {\n            opacity: 0,\n            left: x,\n            top: y\n        },\n        animate: {\n            opacity: [\n                0,\n                1,\n                0\n            ],\n            scale: [\n                0,\n                scale,\n                0\n            ],\n            rotate: [\n                75,\n                120,\n                150\n            ]\n        },\n        transition: {\n            duration: 0.8,\n            repeat: Infinity,\n            delay\n        },\n        width: \"21\",\n        height: \"21\",\n        viewBox: \"0 0 21 21\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M9.82531 0.843845C10.0553 0.215178 10.9446 0.215178 11.1746 0.843845L11.8618 2.72026C12.4006 4.19229 12.3916 6.39157 13.5 7.5C14.6084 8.60843 16.8077 8.59935 18.2797 9.13822L20.1561 9.82534C20.7858 10.0553 20.7858 10.9447 20.1561 11.1747L18.2797 11.8618C16.8077 12.4007 14.6084 12.3916 13.5 13.5C12.3916 14.6084 12.4006 16.8077 11.8618 18.2798L11.1746 20.1562C10.9446 20.7858 10.0553 20.7858 9.82531 20.1562L9.13819 18.2798C8.59932 16.8077 8.60843 14.6084 7.5 13.5C6.39157 12.3916 4.19225 12.4007 2.72023 11.8618L0.843814 11.1747C0.215148 10.9447 0.215148 10.0553 0.843814 9.82534L2.72023 9.13822C4.19225 8.59935 6.39157 8.60843 7.5 7.5C8.60843 6.39157 8.59932 4.19229 9.13819 2.72026L9.82531 0.843845Z\",\n            fill: color\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, id, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\nconst SparklesText = ({ children, colors = {\n    first: \"#d4af37\",\n    second: \"#ffd700\"\n}, className, sparklesCount = 10, ...props })=>{\n    const [sparkles, setSparkles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const generateStar = ()=>{\n            const starX = `${Math.random() * 100}%`;\n            const starY = `${Math.random() * 100}%`;\n            const color = Math.random() > 0.5 ? colors.first : colors.second;\n            const delay = Math.random() * 2;\n            const scale = Math.random() * 1 + 0.3;\n            const lifespan = Math.random() * 10 + 5;\n            const id = `${starX}-${starY}-${Date.now()}`;\n            return {\n                id,\n                x: starX,\n                y: starY,\n                color,\n                delay,\n                scale,\n                lifespan\n            };\n        };\n        const initializeStars = ()=>{\n            const newSparkles = Array.from({\n                length: sparklesCount\n            }, generateStar);\n            setSparkles(newSparkles);\n        };\n        const updateStars = ()=>{\n            setSparkles((currentSparkles)=>currentSparkles.map((star)=>{\n                    if (star.lifespan <= 0) {\n                        return generateStar();\n                    } else {\n                        return {\n                            ...star,\n                            lifespan: star.lifespan - 0.1\n                        };\n                    }\n                }));\n        };\n        initializeStars();\n        const interval = setInterval(updateStars, 100);\n        return ()=>clearInterval(interval);\n    }, [\n        colors.first,\n        colors.second,\n        sparklesCount\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-6xl font-bold\", className),\n        ...props,\n        style: {\n            \"--sparkles-first-color\": `${colors.first}`,\n            \"--sparkles-second-color\": `${colors.second}`\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"relative inline-block\",\n            children: [\n                sparkles.map((sparkle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sparkle, {\n                        ...sparkle\n                    }, sparkle.id, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined)),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sparkles-text.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/typing-animation.tsx":
/*!********************************************!*\
  !*** ./components/ui/typing-animation.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingAnimation: () => (/* binding */ TypingAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ TypingAnimation auto */ \n\n\n\nfunction TypingAnimation({ children, className, duration = 100, delay = 0, as: Component = \"div\", startOnView = false, ...props }) {\n    const MotionComponent = framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.create(Component, {\n        forwardMotionProps: true\n    });\n    const [displayedText, setDisplayedText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [started, setStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!startOnView) {\n            const startTimeout = setTimeout(()=>{\n                setStarted(true);\n            }, delay);\n            return ()=>clearTimeout(startTimeout);\n        }\n        const observer = new IntersectionObserver(([entry])=>{\n            if (entry.isIntersecting) {\n                setTimeout(()=>{\n                    setStarted(true);\n                }, delay);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1\n        });\n        if (elementRef.current) {\n            observer.observe(elementRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        delay,\n        startOnView\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!started) return;\n        let i = 0;\n        const typingEffect = setInterval(()=>{\n            if (i < children.length) {\n                setDisplayedText(children.substring(0, i + 1));\n                i++;\n            } else {\n                clearInterval(typingEffect);\n            }\n        }, duration);\n        return ()=>{\n            clearInterval(typingEffect);\n        };\n    }, [\n        children,\n        duration,\n        started\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MotionComponent, {\n        ref: elementRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-4xl font-bold leading-[5rem] tracking-[-0.02em]\", className),\n        ...props,\n        children: [\n            displayedText,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                animate: {\n                    opacity: [\n                        1,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 0.8,\n                    repeat: Infinity,\n                    repeatType: \"reverse\"\n                },\n                className: \"inline-block\",\n                children: \"|\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\typing-animation.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\typing-animation.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/typing-animation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/word-rotate.tsx":
/*!***************************************!*\
  !*** ./components/ui/word-rotate.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WordRotate: () => (/* binding */ WordRotate)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ WordRotate auto */ \n\n\n\nfunction WordRotate({ words, duration = 2500, motionProps = {\n    initial: {\n        opacity: 0,\n        y: -50\n    },\n    animate: {\n        opacity: 1,\n        y: 0\n    },\n    exit: {\n        opacity: 0,\n        y: 50\n    },\n    transition: {\n        duration: 0.25,\n        ease: \"easeOut\"\n    }\n}, className }) {\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setIndex((prevIndex)=>(prevIndex + 1) % words.length);\n        }, duration);\n        // Clean up interval on unmount\n        return ()=>clearInterval(interval);\n    }, [\n        words,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden py-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n            mode: \"wait\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(className),\n                ...motionProps,\n                children: words[index]\n            }, words[index], false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/word-rotate.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXN0dWRpbzczMC1saW5rLW1vZGVybml6YWRvLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"66b19ff14437\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lc3R1ZGlvNzMwLWxpbmstbW9kZXJuaXphZG8vLi9hcHAvZ2xvYmFscy5jc3M/ZDQ0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY2YjE5ZmYxNDQzN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Est\\xfadio730 - Links\",\n    description: \"Todos os links do Est\\xfadio730 em um s\\xf3 lugar\",\n    icons: {\n        icon: \"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✂️</text></svg>\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQVFmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsT0FBTztRQUNMQyxNQUFNO0lBQ1I7QUFDRixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBUUMsV0FBVTtrQkFDM0IsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFViwyTUFBZ0IsQ0FBQyxVQUFVLENBQUM7c0JBQzdDTzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL2VzdHVkaW83MzAtbGluay1tb2Rlcm5pemFkby8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBQb3BwaW5zIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgcG9wcGlucyA9IFBvcHBpbnMoe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHdlaWdodDogWyczMDAnLCAnNDAwJywgJzYwMCcsICc3MDAnXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtcG9wcGlucycsXG59KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0VzdMO6ZGlvNzMwIC0gTGlua3MnLFxuICBkZXNjcmlwdGlvbjogJ1RvZG9zIG9zIGxpbmtzIGRvIEVzdMO6ZGlvNzMwIGVtIHVtIHPDsyBsdWdhcicsXG4gIGljb25zOiB7XG4gICAgaWNvbjogXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDAgMTAwJz48dGV4dCB5PScuOWVtJyBmb250LXNpemU9JzkwJz7inILvuI88L3RleHQ+PC9zdmc+XCIsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJwdC1CUlwiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7cG9wcGlucy52YXJpYWJsZX0gZm9udC1zYW5zYH0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJwb3BwaW5zIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbnMiLCJpY29uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5IiwidmFyaWFibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Projetos\link\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();