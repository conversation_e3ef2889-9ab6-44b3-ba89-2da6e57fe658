/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1Q2xpbmslNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXN0dWRpbzczMC1saW5rLW1vZGVybml6YWRvLz81NzUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamV0b3NcXFxcbGlua1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5Clink%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/shimmer-button */ \"(ssr)/./components/ui/shimmer-button.tsx\");\n/* harmony import */ var _components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ripple-button */ \"(ssr)/./components/ui/ripple-button.tsx\");\n/* harmony import */ var _components_ui_pulsating_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pulsating-button */ \"(ssr)/./components/ui/pulsating-button.tsx\");\n/* harmony import */ var _components_ui_sparkles_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sparkles-text */ \"(ssr)/./components/ui/sparkles-text.tsx\");\n/* harmony import */ var _components_ui_typing_animation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/typing-animation */ \"(ssr)/./components/ui/typing-animation.tsx\");\n/* harmony import */ var _components_ui_word_rotate__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/word-rotate */ \"(ssr)/./components/ui/word-rotate.tsx\");\n/* harmony import */ var _components_ui_animated_gradient_text__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/animated-gradient-text */ \"(ssr)/./components/ui/animated-gradient-text.tsx\");\n/* harmony import */ var _components_ui_retro_grid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/retro-grid */ \"(ssr)/./components/ui/retro-grid.tsx\");\n/* harmony import */ var _components_ui_ripple__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ripple */ \"(ssr)/./components/ui/ripple.tsx\");\n/* harmony import */ var _components_ui_border_beam__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/border-beam */ \"(ssr)/./components/ui/border-beam.tsx\");\n/* harmony import */ var _components_ui_meteors__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/meteors */ \"(ssr)/./components/ui/meteors.tsx\");\n/* harmony import */ var _components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/magic-card */ \"(ssr)/./components/ui/magic-card.tsx\");\n/* harmony import */ var _components_ui_neon_gradient_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/neon-gradient-card */ \"(ssr)/./components/ui/neon-gradient-card.tsx\");\n/* harmony import */ var _components_ui_particles__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/particles */ \"(ssr)/./components/ui/particles.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Configurações dos links - EDITE AQUI PARA PERSONALIZAR\nconst LINKS_CONFIG = {\n    whatsapp: {\n        phone: \"5511999999999\",\n        message: \"Ol\\xe1! Gostaria de agendar um hor\\xe1rio no Est\\xfadio730.\"\n    },\n    instagram: {\n        username: \"estudio730\"\n    },\n    location: {\n        address: \"Rua das Flores, 123, S\\xe3o Paulo, SP\"\n    },\n    website: {\n        url: \"https://www.estudio730.com.br\"\n    }\n};\n// Função para gerar link do WhatsApp\nfunction generateWhatsAppLink(phone, message) {\n    const encodedMessage = encodeURIComponent(message);\n    return `https://wa.me/${phone}?text=${encodedMessage}`;\n}\n// Função para gerar link do Instagram\nfunction generateInstagramLink(username) {\n    return `https://www.instagram.com/${username}/`;\n}\n// Função para gerar link do Google Maps\nfunction generateLocationLink(address) {\n    const encodedAddress = encodeURIComponent(address);\n    return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;\n}\nfunction Home() {\n    const [configOpen, setConfigOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLinkClick = (type)=>{\n        let url = \"\";\n        switch(type){\n            case \"whatsapp\":\n                url = generateWhatsAppLink(LINKS_CONFIG.whatsapp.phone, LINKS_CONFIG.whatsapp.message);\n                break;\n            case \"instagram\":\n                url = generateInstagramLink(LINKS_CONFIG.instagram.username);\n                break;\n            case \"location\":\n                url = generateLocationLink(LINKS_CONFIG.location.address);\n                break;\n            case \"website\":\n                url = LINKS_CONFIG.website.url;\n                break;\n        }\n        if (url) {\n            window.open(url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_retro_grid__WEBPACK_IMPORTED_MODULE_10__.RetroGrid, {\n                className: \"opacity-20\",\n                angle: 65,\n                cellSize: 60,\n                lightLineColor: \"#d4af37\",\n                darkLineColor: \"#d4af37\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple__WEBPACK_IMPORTED_MODULE_11__.Ripple, {\n                mainCircleSize: 300,\n                mainCircleOpacity: 0.1,\n                numCircles: 6,\n                className: \"opacity-30\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_particles__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"absolute inset-0 hidden sm:block\",\n                quantity: 50,\n                ease: 80,\n                color: \"#d4af37\",\n                size: 0.8\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_particles__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"absolute inset-0\",\n                    quantity: 25,\n                    ease: 60,\n                    color: \"#d4af37\",\n                    size: 0.6\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden sm:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_meteors__WEBPACK_IMPORTED_MODULE_13__.Meteors, {\n                    number: 15\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_meteors__WEBPACK_IMPORTED_MODULE_13__.Meteors, {\n                    number: 8\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                    className: \"w-12 h-12 p-0 border-gray-700/30\",\n                    gradientColor: \"#d4af37\",\n                    gradientOpacity: 0.2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full h-full bg-gray-800/90 backdrop-blur-sm border-none rounded-xl text-white hover:bg-gray-700/90 transition-all duration-300 flex items-center justify-center group\",\n                        onClick: ()=>setConfigOpen(!configOpen),\n                        title: \"Configura\\xe7\\xf5es\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 group-hover:rotate-90 transition-transform duration-300\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-md mx-auto px-4 sm:px-5 py-8 sm:py-10 min-h-screen flex flex-col justify-center gap-6 sm:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto mb-4 sm:mb-6 w-fit\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_neon_gradient_card__WEBPACK_IMPORTED_MODULE_15__.NeonGradientCard, {\n                                    className: \"w-32 h-32 sm:w-40 sm:h-40 hover:scale-105 transition-transform duration-300\",\n                                    borderSize: 3,\n                                    borderRadius: 80,\n                                    neonColors: {\n                                        firstColor: \"#d4af37\",\n                                        secondColor: \"#ffd700\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full rounded-full overflow-hidden relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-transparent rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: \"/logo.webp\",\n                                                alt: \"Est\\xfadio730 Logo\",\n                                                width: 160,\n                                                height: 160,\n                                                className: \"w-full h-full object-cover rounded-full\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sparkles_text__WEBPACK_IMPORTED_MODULE_6__.SparklesText, {\n                                className: \"text-3xl sm:text-4xl md:text-5xl font-bold mb-2 sm:mb-3\",\n                                colors: {\n                                    first: \"#d4af37\",\n                                    second: \"#ffd700\"\n                                },\n                                sparklesCount: 15,\n                                children: \"Est\\xfadio730\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_word_rotate__WEBPACK_IMPORTED_MODULE_8__.WordRotate, {\n                                words: [\n                                    \"Estilo e tradi\\xe7\\xe3o em cada corte\",\n                                    \"Excel\\xeancia em barbearia moderna\",\n                                    \"Seu visual, nossa especialidade\",\n                                    \"Cortes que fazem a diferen\\xe7a\"\n                                ],\n                                className: \"text-gray-300 text-base sm:text-lg px-2\",\n                                duration: 3000\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"space-y-3 sm:space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                                className: \"w-full h-14 sm:h-16 p-0 border-green-500/30\",\n                                gradientColor: \"#25d366\",\n                                gradientOpacity: 0.3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_shimmer_button__WEBPACK_IMPORTED_MODULE_3__.ShimmerButton, {\n                                        onClick: ()=>handleLinkClick(\"whatsapp\"),\n                                        className: \"w-full h-full bg-green-600 hover:bg-green-700 border-none text-white font-semibold text-base sm:text-lg\",\n                                        shimmerColor: \"#25d366\",\n                                        background: \"linear-gradient(135deg, #25d366, #128c7e)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full px-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"WhatsApp\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_gradient_text__WEBPACK_IMPORTED_MODULE_9__.AnimatedGradientText, {\n                                                                    className: \"text-xs px-2 py-0.5\",\n                                                                    colors: [\n                                                                        \"#25d366\",\n                                                                        \"#128c7e\",\n                                                                        \"#25d366\"\n                                                                    ],\n                                                                    animationSpeed: 3,\n                                                                    children: \"Agende seu hor\\xe1rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 opacity-60\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_border_beam__WEBPACK_IMPORTED_MODULE_12__.BorderBeam, {\n                                        size: 300,\n                                        duration: 12,\n                                        colorFrom: \"#25d366\",\n                                        colorTo: \"#128c7e\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                                className: \"w-full h-14 sm:h-16 p-0 border-purple-500/30\",\n                                gradientColor: \"#e4405f\",\n                                gradientOpacity: 0.3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__.RippleButton, {\n                                        onClick: ()=>handleLinkClick(\"instagram\"),\n                                        className: \"w-full h-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 border-none text-white font-semibold text-base sm:text-lg\",\n                                        rippleColor: \"#e4405f\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full px-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"Instagram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm opacity-80\",\n                                                                    children: \"Veja nossos trabalhos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 opacity-60\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_border_beam__WEBPACK_IMPORTED_MODULE_12__.BorderBeam, {\n                                        size: 300,\n                                        duration: 15,\n                                        colorFrom: \"#e4405f\",\n                                        colorTo: \"#9c40ff\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                                className: \"w-full h-14 sm:h-16 p-0 border-blue-500/30\",\n                                gradientColor: \"#4285f4\",\n                                gradientOpacity: 0.3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pulsating_button__WEBPACK_IMPORTED_MODULE_5__.PulsatingButton, {\n                                        onClick: ()=>handleLinkClick(\"location\"),\n                                        className: \"w-full h-full bg-blue-600 hover:bg-blue-700 border-none text-white font-semibold text-base sm:text-lg\",\n                                        pulseColor: \"#4285f4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full px-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"Localiza\\xe7\\xe3o\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm opacity-80\",\n                                                                    children: \"Como chegar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 opacity-60\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_border_beam__WEBPACK_IMPORTED_MODULE_12__.BorderBeam, {\n                                        size: 300,\n                                        duration: 18,\n                                        colorFrom: \"#4285f4\",\n                                        colorTo: \"#1976d2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                                className: \"w-full h-14 sm:h-16 p-0 border-yellow-500/30\",\n                                gradientColor: \"#d4af37\",\n                                gradientOpacity: 0.3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ripple_button__WEBPACK_IMPORTED_MODULE_4__.RippleButton, {\n                                        onClick: ()=>handleLinkClick(\"website\"),\n                                        className: \"w-full h-full bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 border-none text-black font-semibold text-base sm:text-lg\",\n                                        rippleColor: \"#d4af37\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full px-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"Site Oficial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm opacity-80\",\n                                                                    children: \"Conhe\\xe7a mais\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 opacity-60\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_border_beam__WEBPACK_IMPORTED_MODULE_12__.BorderBeam, {\n                                        size: 300,\n                                        duration: 20,\n                                        colorFrom: \"#d4af37\",\n                                        colorTo: \"#ffd700\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"text-center text-gray-400 text-xs sm:text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_magic_card__WEBPACK_IMPORTED_MODULE_14__.MagicCard, {\n                            className: \"p-3 sm:p-4 border-gray-700/30 bg-gray-900/50 backdrop-blur-sm\",\n                            gradientColor: \"#d4af37\",\n                            gradientOpacity: 0.2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typing_animation__WEBPACK_IMPORTED_MODULE_7__.TypingAnimation, {\n                                    className: \"text-sm\",\n                                    duration: 80,\n                                    delay: 2000,\n                                    startOnView: true,\n                                    children: \"\\xa9 2024 Est\\xfadio730. Todos os direitos reservados.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-4 sm:gap-6 mt-3 sm:mt-4 opacity-60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-yellow-400 transition-all duration-300 cursor-pointer\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-blue-400 transition-all duration-300 cursor-pointer\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 sm:w-5 sm:h-5 hover:opacity-100 hover:text-green-400 transition-all duration-300 cursor-pointer\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\page.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/animated-gradient-text.tsx":
/*!**************************************************!*\
  !*** ./components/ui/animated-gradient-text.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedGradientText: () => (/* binding */ AnimatedGradientText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction AnimatedGradientText({ children, className, colors = [\n    \"#ffaa40\",\n    \"#9c40ff\",\n    \"#ffaa40\"\n], animationSpeed = 8, showBorder = false, ...props }) {\n    const gradientStyle = {\n        \"--color-one\": colors[0],\n        \"--color-two\": colors[1] || colors[0],\n        \"--color-three\": colors[2] || colors[0],\n        \"--speed\": `${animationSpeed}s`\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"group relative mx-auto flex max-w-fit flex-row items-center justify-center rounded-2xl bg-white/40 px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#8fdfff1f] backdrop-blur-sm transition-shadow duration-500 ease-out [--bg-size:300%] hover:shadow-[inset_0_-5px_10px_#8fdfff3f] dark:bg-black/40\", className),\n        style: gradientStyle,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 block h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] p-[1px] ![mask-composite:subtract] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)]`\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            showBorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 z-10 h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] ![mask-composite:subtract]`\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\animated-gradient-text.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/animated-gradient-text.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/border-beam.tsx":
/*!***************************************!*\
  !*** ./components/ui/border-beam.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BorderBeam: () => (/* binding */ BorderBeam)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nconst BorderBeam = ({ className, size = 200, duration = 15, anchor = 90, borderWidth = 1.5, colorFrom = \"#ffaa40\", colorTo = \"#9c40ff\", delay = 0 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            \"--size\": size,\n            \"--duration\": duration,\n            \"--anchor\": anchor,\n            \"--border-width\": borderWidth,\n            \"--color-from\": colorFrom,\n            \"--color-to\": colorTo,\n            \"--delay\": `-${delay}s`\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none absolute inset-0 rounded-[inherit] [border:calc(var(--border-width)*1px)_solid_transparent]\", // mask styles\n        \"[mask-clip:padding-box,border-box] [mask-composite:intersect] [mask:linear-gradient(transparent,transparent),linear-gradient(white,white)]\", // pseudo styles\n        \"after:absolute after:aspect-square after:w-[calc(var(--size)*1px)] after:animate-border-beam after:[animation-delay:var(--delay)] after:[background:linear-gradient(to_left,var(--color-from),var(--color-to),transparent)] after:[offset-anchor:calc(var(--anchor)*1%)_50%] after:[offset-path:rect(0_auto_auto_0_round_calc(var(--size)*1px))]\", className)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\border-beam.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/border-beam.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/magic-card.tsx":
/*!**************************************!*\
  !*** ./components/ui/magic-card.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MagicCard: () => (/* binding */ MagicCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-template.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ MagicCard auto */ \n\n\n\nfunction MagicCard({ children, className, gradientSize = 200, gradientColor = \"#d4af37\", gradientOpacity = 0.8 }) {\n    const mouseX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useMotionValue)(0);\n    const mouseY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useMotionValue)(0);\n    function handleMouseMove({ currentTarget, clientX, clientY }) {\n        const { left, top } = currentTarget.getBoundingClientRect();\n        mouseX.set(clientX - left);\n        mouseY.set(clientY - top);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseMove: handleMouseMove,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"group relative flex size-full overflow-hidden rounded-xl bg-neutral-100 dark:bg-neutral-900 border text-black dark:text-white\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\magic-card.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"pointer-events-none absolute -inset-px rounded-xl opacity-0 transition duration-300 group-hover:opacity-100\",\n                style: {\n                    background: (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useMotionTemplate)`\n            radial-gradient(${gradientSize}px circle at ${mouseX}px ${mouseY}px, ${gradientColor}${Math.round(gradientOpacity * 255).toString(16)}, transparent 100%)\n          `\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\magic-card.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\magic-card.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/magic-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/meteors.tsx":
/*!***********************************!*\
  !*** ./components/ui/meteors.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Meteors: () => (/* binding */ Meteors)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Meteors auto */ \n\n\nconst Meteors = ({ number = 20, className })=>{\n    const meteors = new Array(number).fill(true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: meteors.map((el, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-meteor-effect absolute top-1/2 left-1/2 h-0.5 w-0.5 rounded-[9999px] bg-slate-500 shadow-[0_0_0_1px_#ffffff10] rotate-[215deg]\", \"before:content-[''] before:absolute before:top-1/2 before:transform before:-translate-y-[50%] before:w-[50px] before:h-[1px] before:bg-gradient-to-r before:from-[#64748b] before:to-transparent\", className),\n                style: {\n                    top: 0,\n                    left: Math.floor(Math.random() * (400 - -400) + -400) + \"px\",\n                    animationDelay: Math.random() * (0.8 - 0.2) + 0.2 + \"s\",\n                    animationDuration: Math.floor(Math.random() * (10 - 2) + 2) + \"s\"\n                }\n            }, \"meteor\" + idx, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\meteors.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/meteors.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/neon-gradient-card.tsx":
/*!**********************************************!*\
  !*** ./components/ui/neon-gradient-card.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NeonGradientCard: () => (/* binding */ NeonGradientCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ NeonGradientCard auto */ \n\n\nconst NeonGradientCard = ({ className, children, borderSize = 2, borderRadius = 20, neonColors = {\n    firstColor: \"#d4af37\",\n    secondColor: \"#ffd700\"\n}, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative z-10 h-fit w-fit rounded-[--border-radius] bg-white p-[--border-size] dark:bg-black\", className),\n        style: {\n            \"--border-size\": `${borderSize}px`,\n            \"--border-radius\": `${borderRadius}px`\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `animate-spin-around absolute -inset-[--border-size] -z-10 h-[calc(100%+calc(var(--border-size)*2))] w-[calc(100%+calc(var(--border-size)*2))] rounded-[--border-radius] bg-[conic-gradient(0deg,transparent,${neonColors.secondColor},transparent,${neonColors.firstColor},transparent,${neonColors.secondColor},transparent)] [animation-duration:4s]`\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\neon-gradient-card.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `relative z-20 h-full w-full rounded-[calc(var(--border-radius)-var(--border-size))] bg-gray-100 dark:bg-gray-900`,\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\neon-gradient-card.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\neon-gradient-card.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/neon-gradient-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/particles.tsx":
/*!*************************************!*\
  !*** ./components/ui/particles.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Particles)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Particles({ className = \"\", quantity = 30, staticity = 50, ease = 50, size = 0.4, refresh = false, color = \"#d4af37\", vx = 0, vy = 0 }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const circles = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const mousePosition = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const mouseMoved = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const [canvasSize, setCanvasSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        w: 0,\n        h: 0\n    });\n    const dpr =  false ? 0 : 1;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (canvasRef.current) {\n            context.current = canvasRef.current.getContext(\"2d\");\n        }\n        initCanvas();\n        animate();\n        const handleResize = ()=>initCanvas();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        color\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        onMouseMove();\n    }, [\n        staticity,\n        ease\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        initCanvas();\n    }, [\n        refresh\n    ]);\n    const initCanvas = ()=>{\n        resizeCanvas();\n        drawParticles();\n    };\n    const onMouseMove = ()=>{\n        if (canvasRef.current) {\n            const rect = canvasRef.current.getBoundingClientRect();\n            const { w, h } = canvasSize;\n            const x = mousePosition.current.x - rect.left - w / 2;\n            const y = mousePosition.current.y - rect.top - h / 2;\n            const inside = x < w / 2 && x > -w / 2 && y < h / 2 && y > -h / 2;\n            if (inside) {\n                mouseMoved.current = true;\n                circles.current.forEach((circle)=>{\n                    const dx = circle.x - x;\n                    const dy = circle.y - y;\n                    const d = Math.sqrt(dx * dx + dy * dy);\n                    const maxDistance = staticity;\n                    const force = (maxDistance - d) / maxDistance;\n                    const directionX = dx / d;\n                    const directionY = dy / d;\n                    const velocityX = directionX * force * ease;\n                    const velocityY = directionY * force * ease;\n                    circle.translateX += velocityX;\n                    circle.translateY += velocityY;\n                });\n            }\n        }\n    };\n    const resizeCanvas = ()=>{\n        if (canvasContainerRef.current && canvasRef.current && context.current) {\n            circles.current.length = 0;\n            canvasRef.current.width = canvasContainerRef.current.offsetWidth * dpr;\n            canvasRef.current.height = canvasContainerRef.current.offsetHeight * dpr;\n            canvasRef.current.style.width = canvasContainerRef.current.offsetWidth + \"px\";\n            canvasRef.current.style.height = canvasContainerRef.current.offsetHeight + \"px\";\n            context.current.scale(dpr, dpr);\n            setCanvasSize({\n                w: canvasContainerRef.current.offsetWidth,\n                h: canvasContainerRef.current.offsetHeight\n            });\n        }\n    };\n    const circleParams = ()=>{\n        const x = Math.floor(Math.random() * canvasSize.w);\n        const y = Math.floor(Math.random() * canvasSize.h);\n        const translateX = 0;\n        const translateY = 0;\n        const pSize = Math.floor(Math.random() * 2) + size;\n        const alpha = 0;\n        const targetAlpha = parseFloat((Math.random() * 0.6 + 0.1).toFixed(1));\n        const dx = (Math.random() - 0.5) * 0.1;\n        const dy = (Math.random() - 0.5) * 0.1;\n        const magnetism = 0.1 + Math.random() * 4;\n        return {\n            x,\n            y,\n            translateX,\n            translateY,\n            size: pSize,\n            alpha,\n            targetAlpha,\n            dx,\n            dy,\n            magnetism\n        };\n    };\n    const rgb = (color)=>{\n        return color.match(/\\w\\w/g)?.map((x)=>parseInt(x, 16));\n    };\n    const drawCircle = (circle, update = false)=>{\n        if (context.current) {\n            const { x, y, translateX, translateY, size, alpha } = circle;\n            context.current.translate(translateX, translateY);\n            context.current.beginPath();\n            context.current.arc(x, y, size, 0, 2 * Math.PI);\n            const rgbColor = rgb(color);\n            context.current.fillStyle = `rgba(${rgbColor?.[0]}, ${rgbColor?.[1]}, ${rgbColor?.[2]}, ${alpha})`;\n            context.current.fill();\n            context.current.setTransform(dpr, 0, 0, dpr, 0, 0);\n            if (!update) {\n                circles.current.push(circle);\n            }\n        }\n    };\n    const clearContext = ()=>{\n        if (context.current) {\n            context.current.clearRect(0, 0, canvasSize.w, canvasSize.h);\n        }\n    };\n    const drawParticles = ()=>{\n        clearContext();\n        const particleCount = quantity;\n        for(let i = 0; i < particleCount; i++){\n            const circle = circleParams();\n            drawCircle(circle);\n        }\n    };\n    const animate = ()=>{\n        clearContext();\n        circles.current.forEach((circle, i)=>{\n            // Handle the alpha value\n            const edge = [\n                circle.x + circle.translateX - circle.size,\n                canvasSize.w - circle.x - circle.translateX - circle.size,\n                circle.y + circle.translateY - circle.size,\n                canvasSize.h - circle.y - circle.translateY - circle.size\n            ];\n            const closestEdge = edge.reduce((a, b)=>Math.min(a, b));\n            const remapClosestEdge = parseFloat(Math.max(closestEdge, 0).toString());\n            const opacity = remapClosestEdge > 20 ? 1 : remapClosestEdge / 20;\n            if (opacity > 0) {\n                circle.alpha += (circle.targetAlpha - circle.alpha) * 0.02;\n                if (circle.alpha > circle.targetAlpha) circle.alpha = circle.targetAlpha;\n            } else {\n                circle.alpha = circle.targetAlpha = 0;\n            }\n            if (circle.alpha === 0) {\n                circle.alpha = circle.targetAlpha = 0;\n            }\n            // Handle the position\n            circle.x += circle.dx + vx;\n            circle.y += circle.dy + vy;\n            circle.translateX += (mousePosition.current.x / (staticity / circle.magnetism) - circle.translateX) / ease;\n            circle.translateY += (mousePosition.current.y / (staticity / circle.magnetism) - circle.translateY) / ease;\n            drawCircle(circle, true);\n            // circle gets out of the canvas\n            if (circle.x < -circle.size || circle.x > canvasSize.w + circle.size || circle.y < -circle.size || circle.y > canvasSize.h + circle.size) {\n                // remove the circle from the array\n                circles.current.splice(i, 1);\n                // create a new circle\n                const newCircle = circleParams();\n                drawCircle(newCircle);\n            // update the circle position\n            }\n        });\n        window.requestAnimationFrame(animate);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none\", className),\n        ref: canvasContainerRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"size-full\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\particles.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\particles.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/particles.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/pulsating-button.tsx":
/*!********************************************!*\
  !*** ./components/ui/pulsating-button.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PulsatingButton: () => (/* binding */ PulsatingButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst PulsatingButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, pulseColor = \"#808080\", duration = \"1.5s\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-pointer items-center justify-center rounded-lg bg-primary px-4 py-2 text-center text-primary-foreground\", className),\n        style: {\n            \"--pulse-color\": pulseColor,\n            \"--duration\": duration\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute left-1/2 top-1/2 size-full -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-lg bg-inherit\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\pulsating-button.tsx\",\n        lineNumber: 25,\n        columnNumber: 7\n    }, undefined);\n});\nPulsatingButton.displayName = \"PulsatingButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/pulsating-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/retro-grid.tsx":
/*!**************************************!*\
  !*** ./components/ui/retro-grid.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RetroGrid: () => (/* binding */ RetroGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction RetroGrid({ className, angle = 65, cellSize = 60, opacity = 0.5, lightLineColor = \"gray\", darkLineColor = \"gray\", ...props }) {\n    const gridStyles = {\n        \"--grid-angle\": `${angle}deg`,\n        \"--cell-size\": `${cellSize}px`,\n        \"--opacity\": opacity,\n        \"--light-line\": lightLineColor,\n        \"--dark-line\": darkLineColor\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"pointer-events-none absolute size-full overflow-hidden [perspective:200px]\", `opacity-[var(--opacity)]`, className),\n        style: gridStyles,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 [transform:rotateX(var(--grid-angle))]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\retro-grid.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/retro-grid.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/ripple-button.tsx":
/*!*****************************************!*\
  !*** ./components/ui/ripple-button.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RippleButton: () => (/* binding */ RippleButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ RippleButton auto */ \n\n\nconst RippleButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef(({ className, children, rippleColor = \"#ffffff\", duration = \"600ms\", onClick, ...props }, ref)=>{\n    const [buttonRipples, setButtonRipples] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const handleClick = (event)=>{\n        createRipple(event);\n        onClick?.(event);\n    };\n    const createRipple = (event)=>{\n        const button = event.currentTarget;\n        const rect = button.getBoundingClientRect();\n        const size = Math.max(rect.width, rect.height);\n        const x = event.clientX - rect.left - size / 2;\n        const y = event.clientY - rect.top - size / 2;\n        const newRipple = {\n            x,\n            y,\n            size,\n            key: Date.now()\n        };\n        setButtonRipples((prevRipples)=>[\n                ...prevRipples,\n                newRipple\n            ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (buttonRipples.length > 0) {\n            const lastRipple = buttonRipples[buttonRipples.length - 1];\n            const timeout = setTimeout(()=>{\n                setButtonRipples((prevRipples)=>prevRipples.filter((ripple)=>ripple.key !== lastRipple.key));\n            }, parseInt(duration));\n            return ()=>clearTimeout(timeout);\n        }\n    }, [\n        buttonRipples,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"relative flex cursor-pointer items-center justify-center overflow-hidden rounded-lg border-2 bg-background px-4 py-2 text-center text-primary\", className),\n        onClick: handleClick,\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute inset-0\",\n                children: buttonRipples.map((ripple)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute animate-rippling rounded-full bg-background opacity-30\",\n                        style: {\n                            width: `${ripple.size}px`,\n                            height: `${ripple.size}px`,\n                            top: `${ripple.y}px`,\n                            left: `${ripple.x}px`,\n                            backgroundColor: rippleColor,\n                            transform: `scale(0)`\n                        }\n                    }, ripple.key, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple-button.tsx\",\n        lineNumber: 60,\n        columnNumber: 7\n    }, undefined);\n});\nRippleButton.displayName = \"RippleButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/ripple-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/ripple.tsx":
/*!**********************************!*\
  !*** ./components/ui/ripple.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ripple: () => (/* binding */ Ripple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Ripple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function Ripple({ mainCircleSize = 210, mainCircleOpacity = 0.24, numCircles = 8, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]\", className),\n        ...props,\n        children: Array.from({\n            length: numCircles\n        }, (_, i)=>{\n            const size = mainCircleSize + i * 70;\n            const opacity = mainCircleOpacity - i * 0.03;\n            const animationDelay = `${i * 0.06}s`;\n            const borderStyle = \"solid\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute animate-ripple rounded-full border bg-foreground/25 shadow-xl`,\n                style: {\n                    \"--i\": i,\n                    width: `${size}px`,\n                    height: `${size}px`,\n                    opacity,\n                    animationDelay,\n                    borderStyle,\n                    borderWidth: \"1px\",\n                    borderColor: `var(--foreground)`,\n                    top: \"50%\",\n                    left: \"50%\",\n                    transform: \"translate(-50%, -50%) scale(1)\"\n                }\n            }, i, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple.tsx\",\n                lineNumber: 33,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\ripple.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n});\nRipple.displayName = \"Ripple\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/ripple.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/shimmer-button.tsx":
/*!******************************************!*\
  !*** ./components/ui/shimmer-button.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShimmerButton: () => (/* binding */ ShimmerButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst ShimmerButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ shimmerColor = \"#ffffff\", shimmerSize = \"0.05em\", shimmerDuration = \"3s\", borderRadius = \"100px\", background = \"rgba(0, 0, 0, 1)\", className, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 px-6 py-3 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\", \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\", className),\n        ref: ref,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-z-30 blur-[2px]\", \"absolute inset-0 overflow-visible [container-type:size]\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"insert-0 absolute size-full\", \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\", // transition\n                \"transform-gpu transition-all duration-300 ease-in-out\", // on hover\n                \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\", // on click\n                \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\")\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\shimmer-button.tsx\",\n        lineNumber: 33,\n        columnNumber: 7\n    }, undefined);\n});\nShimmerButton.displayName = \"ShimmerButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/shimmer-button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sparkles-text.tsx":
/*!*****************************************!*\
  !*** ./components/ui/sparkles-text.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SparklesText: () => (/* binding */ SparklesText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SparklesText auto */ \n\n\n\nconst Sparkle = ({ id, x, y, color, delay, scale })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.svg, {\n        className: \"pointer-events-none absolute z-20\",\n        initial: {\n            opacity: 0,\n            left: x,\n            top: y\n        },\n        animate: {\n            opacity: [\n                0,\n                1,\n                0\n            ],\n            scale: [\n                0,\n                scale,\n                0\n            ],\n            rotate: [\n                75,\n                120,\n                150\n            ]\n        },\n        transition: {\n            duration: 0.8,\n            repeat: Infinity,\n            delay\n        },\n        width: \"21\",\n        height: \"21\",\n        viewBox: \"0 0 21 21\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M9.82531 0.843845C10.0553 0.215178 10.9446 0.215178 11.1746 0.843845L11.8618 2.72026C12.4006 4.19229 12.3916 6.39157 13.5 7.5C14.6084 8.60843 16.8077 8.59935 18.2797 9.13822L20.1561 9.82534C20.7858 10.0553 20.7858 10.9447 20.1561 11.1747L18.2797 11.8618C16.8077 12.4007 14.6084 12.3916 13.5 13.5C12.3916 14.6084 12.4006 16.8077 11.8618 18.2798L11.1746 20.1562C10.9446 20.7858 10.0553 20.7858 9.82531 20.1562L9.13819 18.2798C8.59932 16.8077 8.60843 14.6084 7.5 13.5C6.39157 12.3916 4.19225 12.4007 2.72023 11.8618L0.843814 11.1747C0.215148 10.9447 0.215148 10.0553 0.843814 9.82534L2.72023 9.13822C4.19225 8.59935 6.39157 8.60843 7.5 7.5C8.60843 6.39157 8.59932 4.19229 9.13819 2.72026L9.82531 0.843845Z\",\n            fill: color\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, id, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\nconst SparklesText = ({ children, colors = {\n    first: \"#d4af37\",\n    second: \"#ffd700\"\n}, className, sparklesCount = 10, ...props })=>{\n    const [sparkles, setSparkles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const generateStar = ()=>{\n            const starX = `${Math.random() * 100}%`;\n            const starY = `${Math.random() * 100}%`;\n            const color = Math.random() > 0.5 ? colors.first : colors.second;\n            const delay = Math.random() * 2;\n            const scale = Math.random() * 1 + 0.3;\n            const lifespan = Math.random() * 10 + 5;\n            const id = `${starX}-${starY}-${Date.now()}`;\n            return {\n                id,\n                x: starX,\n                y: starY,\n                color,\n                delay,\n                scale,\n                lifespan\n            };\n        };\n        const initializeStars = ()=>{\n            const newSparkles = Array.from({\n                length: sparklesCount\n            }, generateStar);\n            setSparkles(newSparkles);\n        };\n        const updateStars = ()=>{\n            setSparkles((currentSparkles)=>currentSparkles.map((star)=>{\n                    if (star.lifespan <= 0) {\n                        return generateStar();\n                    } else {\n                        return {\n                            ...star,\n                            lifespan: star.lifespan - 0.1\n                        };\n                    }\n                }));\n        };\n        initializeStars();\n        const interval = setInterval(updateStars, 100);\n        return ()=>clearInterval(interval);\n    }, [\n        colors.first,\n        colors.second,\n        sparklesCount\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-6xl font-bold\", className),\n        ...props,\n        style: {\n            \"--sparkles-first-color\": `${colors.first}`,\n            \"--sparkles-second-color\": `${colors.second}`\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"relative inline-block\",\n            children: [\n                sparkles.map((sparkle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sparkle, {\n                        ...sparkle\n                    }, sparkle.id, false, {\n                        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined)),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\sparkles-text.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sparkles-text.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/typing-animation.tsx":
/*!********************************************!*\
  !*** ./components/ui/typing-animation.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingAnimation: () => (/* binding */ TypingAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ TypingAnimation auto */ \n\n\n\nfunction TypingAnimation({ children, className, duration = 100, delay = 0, as: Component = \"div\", startOnView = false, ...props }) {\n    const MotionComponent = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion)(Component);\n    const [displayedText, setDisplayedText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [started, setStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!startOnView) {\n            const startTimeout = setTimeout(()=>{\n                setStarted(true);\n            }, delay);\n            return ()=>clearTimeout(startTimeout);\n        }\n        const observer = new IntersectionObserver(([entry])=>{\n            if (entry.isIntersecting) {\n                setTimeout(()=>{\n                    setStarted(true);\n                }, delay);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1\n        });\n        if (elementRef.current) {\n            observer.observe(elementRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        delay,\n        startOnView\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!started) return;\n        let i = 0;\n        const typingEffect = setInterval(()=>{\n            if (i < children.length) {\n                setDisplayedText(children.substring(0, i + 1));\n                i++;\n            } else {\n                clearInterval(typingEffect);\n            }\n        }, duration);\n        return ()=>{\n            clearInterval(typingEffect);\n        };\n    }, [\n        children,\n        duration,\n        started\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MotionComponent, {\n        ref: elementRef,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-4xl font-bold leading-[5rem] tracking-[-0.02em]\", className),\n        ...props,\n        children: [\n            displayedText,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                animate: {\n                    opacity: [\n                        1,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 0.8,\n                    repeat: Infinity,\n                    repeatType: \"reverse\"\n                },\n                className: \"inline-block\",\n                children: \"|\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\typing-animation.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\typing-animation.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/typing-animation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/word-rotate.tsx":
/*!***************************************!*\
  !*** ./components/ui/word-rotate.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WordRotate: () => (/* binding */ WordRotate)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ WordRotate auto */ \n\n\n\nfunction WordRotate({ words, duration = 2500, motionProps = {\n    initial: {\n        opacity: 0,\n        y: -50\n    },\n    animate: {\n        opacity: 1,\n        y: 0\n    },\n    exit: {\n        opacity: 0,\n        y: 50\n    },\n    transition: {\n        duration: 0.25,\n        ease: \"easeOut\"\n    }\n}, className }) {\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setIndex((prevIndex)=>(prevIndex + 1) % words.length);\n        }, duration);\n        // Clean up interval on unmount\n        return ()=>clearInterval(interval);\n    }, [\n        words,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-hidden py-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n            mode: \"wait\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(className),\n                ...motionProps,\n                children: words[index]\n            }, words[index], false, {\n                fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\components\\\\ui\\\\word-rotate.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/word-rotate.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXN0dWRpbzczMC1saW5rLW1vZGVybml6YWRvLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e2e7b5b022e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lc3R1ZGlvNzMwLWxpbmstbW9kZXJuaXphZG8vLi9hcHAvZ2xvYmFscy5jc3M/ZDQ0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMmU3YjViMDIyZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Est\\xfadio730 - Links\",\n    description: \"Todos os links do Est\\xfadio730 em um s\\xf3 lugar\",\n    icons: {\n        icon: \"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✂️</text></svg>\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\link\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQVFmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsT0FBTztRQUNMQyxNQUFNO0lBQ1I7QUFDRixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBUUMsV0FBVTtrQkFDM0IsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFViwyTUFBZ0IsQ0FBQyxVQUFVLENBQUM7c0JBQzdDTzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL2VzdHVkaW83MzAtbGluay1tb2Rlcm5pemFkby8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBQb3BwaW5zIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgcG9wcGlucyA9IFBvcHBpbnMoe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHdlaWdodDogWyczMDAnLCAnNDAwJywgJzYwMCcsICc3MDAnXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtcG9wcGlucycsXG59KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0VzdMO6ZGlvNzMwIC0gTGlua3MnLFxuICBkZXNjcmlwdGlvbjogJ1RvZG9zIG9zIGxpbmtzIGRvIEVzdMO6ZGlvNzMwIGVtIHVtIHPDsyBsdWdhcicsXG4gIGljb25zOiB7XG4gICAgaWNvbjogXCJkYXRhOmltYWdlL3N2Zyt4bWwsPHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMDAgMTAwJz48dGV4dCB5PScuOWVtJyBmb250LXNpemU9JzkwJz7inILvuI88L3RleHQ+PC9zdmc+XCIsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJwdC1CUlwiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7cG9wcGlucy52YXJpYWJsZX0gZm9udC1zYW5zYH0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJwb3BwaW5zIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbnMiLCJpY29uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5IiwidmFyaWFibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Projetos\link\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CProjetos%5Clink%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5Clink&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();