import { ComponentPropsWithoutRef, CSSProperties, ReactNode } from "react";

import { cn } from "@/lib/utils";

interface AnimatedGradientTextProps extends ComponentPropsWithoutRef<"span"> {
  children: ReactNode;
  className?: string;
  colors?: string[];
  animationSpeed?: number;
  showBorder?: boolean;
}

export function AnimatedGradientText({
  children,
  className,
  colors = ["#ffaa40", "#9c40ff", "#ffaa40"],
  animationSpeed = 8,
  showBorder = false,
  ...props
}: AnimatedGradientTextProps) {
  const gradientStyle = {
    "--color-one": colors[0],
    "--color-two": colors[1] || colors[0],
    "--color-three": colors[2] || colors[0],
    "--speed": `${animationSpeed}s`,
  } as CSSProperties;

  return (
    <span
      className={cn(
        "group relative mx-auto flex max-w-fit flex-row items-center justify-center rounded-2xl bg-white/40 px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#8fdfff1f] backdrop-blur-sm transition-shadow duration-500 ease-out [--bg-size:300%] hover:shadow-[inset_0_-5px_10px_#8fdfff3f] dark:bg-black/40",
        className,
      )}
      style={gradientStyle}
      {...props}
    >
      <div
        className={`absolute inset-0 block h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] p-[1px] ![mask-composite:subtract] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)]`}
      />

      {showBorder && (
        <div
          className={`absolute inset-0 z-10 h-full w-full animate-gradient bg-gradient-to-r from-[var(--color-one)] via-[var(--color-two)] to-[var(--color-three)] bg-[length:var(--bg-size)_100%] [border-radius:inherit] [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] ![mask-composite:subtract]`}
        />
      )}

      <span className="relative z-10">{children}</span>
    </span>
  );
}
